{"version": 3, "sources": ["../../src/acrpc/schema.ts"], "sourcesContent": ["import type { Schema } from \"./core\";\n\nimport {\n    Common,\n    Auth,\n    Tag,\n    Commune,\n    Rating,\n    Reactor,\n    User,\n} from \"..\";\nimport { superjsonTransformer } from \"./core\";\n\nexport const CACHE_CONTROL_TEN_MINUTES = \"no-cache\"; // \"max-age=600\";\nexport const CACHE_CONTROL_HOUR = \"no-cache\"; // \"max-age=3600\";\nexport const CACHE_CONTROL_IMMUTABLE = \"no-cache\"; // \"max-age=31536000, immutable\";\n\nexport const DEFAULT_CACHE_CONTROL = CACHE_CONTROL_TEN_MINUTES;\n\nexport const schema = {\n    auth: {\n        otp: {\n            post: {\n                input: Auth.SendOtpInputSchema,\n                output: Auth.SendOtpOutputSchema,\n                isMetadataUsed: false,\n            },\n        },\n        signUp: {\n            post: {\n                input: Auth.SignupInputSchema,\n                output: Auth.SuccessfulOutputSchema,\n                isMetadataUsed: false,\n                invalidate: [\"/user/me\"],\n            },\n        },\n        signIn: {\n            post: {\n                input: Auth.SigninInputSchema,\n                output: Auth.SuccessfulOutputSchema,\n                isMetadataUsed: false,\n                invalidate: [\"/user/me\"],\n            },\n        },\n        signOut: {\n            get: {\n                input: null,\n                output: null,\n                isMetadataUsed: false,\n                invalidate: [\"/user/me\"],\n            },\n        },\n    },\n\n    commune: {\n        transferHeadStatus: {\n            post: {\n                input: Commune.TransferHeadStatusInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 2,\n            },\n        },\n        list: {\n            get: {\n                input: Commune.GetCommunesInputSchema,\n                output: Commune.GetCommunesOutputSchema,\n                cacheControl: DEFAULT_CACHE_CONTROL,\n                isMetadataRequired: false,\n            },\n        },\n        post: {\n            input: Commune.CreateCommuneInputSchema,\n            output: Common.ObjectWithIdSchema,\n            autoScopeInvalidationDepth: 1,\n        },\n        patch: {\n            input: Commune.UpdateCommuneInputSchema,\n            output: null,\n            autoScopeInvalidationDepth: 1,\n        },\n        delete: {\n            input: Common.ObjectWithIdSchema,\n            output: null,\n            autoScopeInvalidationDepth: 1,\n        },\n        member: {\n            list: {\n                get: {\n                    input: Commune.GetCommuneMembersInputSchema,\n                    output: Commune.GetCommuneMembersOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                    isMetadataRequired: false,\n                },\n            },\n            delete: {\n                input: Common.ObjectWithIdSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n        },\n        invitation: {\n            list: {\n                get: {\n                    input: Commune.GetCommuneInvitationsInputSchema,\n                    output: Commune.GetCommuneInvitationsOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                },\n            },\n            post: {\n                input: Commune.CreateCommuneInvitationInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: Common.ObjectWithIdSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            accept: {\n                post: {\n                    input: Common.ObjectWithIdSchema,\n                    output: null,\n                    // autoScopeInvalidationDepth: 2,\n                    invalidate: [\"/commune\"],\n                },\n            },\n            reject: {\n                post: {\n                    input: Common.ObjectWithIdSchema,\n                    output: null,\n                    // autoScopeInvalidationDepth: 2,\n                    invalidate: [\"/commune\"],\n                },\n            },\n        },\n        joinRequest: {\n            list: {\n                get: {\n                    input: Commune.GetCommuneJoinRequestsInputSchema,\n                    output: Commune.GetCommuneJoinRequestsOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                },\n            },\n            post: {\n                input: Commune.CreateCommuneJoinRequestInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: Common.ObjectWithIdSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            accept: {\n                post: {\n                    input: Common.ObjectWithIdSchema,\n                    output: null,\n                    // autoScopeInvalidationDepth: 2,\n                    invalidate: [\"/commune\"],\n                },\n            },\n            reject: {\n                post: {\n                    input: Common.ObjectWithIdSchema,\n                    output: null,\n                    // autoScopeInvalidationDepth: 2,\n                    invalidate: [\"/commune\"],\n                },\n            },\n        },\n    },\n\n    rating: {\n        karma: {\n            list: {\n                get: {\n                    input: Rating.GetKarmaPointsInputSchema,\n                    output: Rating.GetKarmaPointsOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                },\n            },\n            post: {\n                input: Rating.SpendKarmaPointInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n                invalidate: [\"/rating/summary\"],\n            },\n        },\n        feedback: {\n            list: {\n                get: {\n                    input: Rating.GetUserFeedbacksInputSchema,\n                    output: Rating.GetUserFeedbacksOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                },\n            },\n            post: {\n                input: Rating.CreateUserFeedbackInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n                invalidate: [\"/rating/summary\"],\n            },\n        },\n        summary: {\n            get: {\n                input: Rating.GetUserSummaryInputSchema,\n                output: Rating.GetUserSummaryOutputSchema,\n                cacheControl: DEFAULT_CACHE_CONTROL,\n            },\n        },\n    },\n\n    reactor: {\n        post: {\n            list: {\n                get: {\n                    input: Reactor.GetPostsInputSchema,\n                    output: Reactor.GetPostsOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                    isMetadataRequired: false,\n                },\n            },\n            post: {\n                input: Reactor.CreatePostInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            patch: {\n                input: Reactor.UpdatePostInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: Reactor.DeletePostInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            rating: {\n                post: {\n                    input: Reactor.UpdatePostRatingInputSchema,\n                    output: Reactor.UpdatePostRatingOutputSchema,\n                    autoScopeInvalidationDepth: 2,\n                },\n            },\n            usefulness: {\n                post: {\n                    input: Reactor.UpdatePostUsefulnessInputSchema,\n                    output: Reactor.UpdatePostUsefulnessOutputSchema,\n                    autoScopeInvalidationDepth: 2,\n                },\n            },\n            image: {\n                list: {\n                    get: {\n                        input: Reactor.GetPostImagesInputSchema,\n                        output: Reactor.GetPostImagesOutputSchema,\n                        cacheControl: DEFAULT_CACHE_CONTROL,\n                    },\n                },\n            },\n        },\n        comment: {\n            list: {\n                get: {\n                    input: Reactor.GetCommentsInputSchema,\n                    output: Reactor.GetCommentsOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                    isMetadataRequired: false,\n                },\n            },\n            post: {\n                input: Reactor.CreateCommentInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            patch: {\n                input: Reactor.UpdateCommentInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: Reactor.DeleteCommentInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            rating: {\n                post: {\n                    input: Reactor.UpdateCommentRatingInputSchema,\n                    output: Reactor.UpdateCommentRatingOutputSchema,\n                    autoScopeInvalidationDepth: 2,\n                },\n            },\n            anonimify: {\n                post: {\n                    input: Reactor.AnonimifyCommentInputSchema,\n                    output: null,\n                    autoScopeInvalidationDepth: 2,\n                },\n            },\n        },\n        lens: {\n            list: {\n                get: {\n                    input: null,\n                    output: Reactor.GetLensesOutputSchema,\n                    cacheControl: CACHE_CONTROL_IMMUTABLE,\n                },\n            },\n            post: {\n                input: Reactor.CreateLensInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            patch: {\n                input: Reactor.UpdateLensInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: Common.ObjectWithIdSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n        },\n        hub: {\n            list: {\n                get: {\n                    input: Reactor.GetHubsInputSchema,\n                    output: Reactor.GetHubsOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                    isMetadataRequired: false,\n                },\n            },\n            post: {\n                input: Reactor.CreateHubInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            patch: {\n                input: Reactor.UpdateHubInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            // delete: {\n            //     input: Common.ObjectWithIdSchema,\n            //     output: null,\n            //     enableAutoScopeInvalidation: true,\n            // },\n        },\n        community: {\n            list: {\n                get: {\n                    input: Reactor.GetCommunitiesInputSchema,\n                    output: Reactor.GetCommunitiesOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                    isMetadataRequired: false,\n                },\n            },\n            post: {\n                input: Reactor.CreateCommunityInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            patch: {\n                input: Reactor.UpdateCommunityInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            // delete: {\n            //     input: Common.ObjectWithIdSchema,\n            //     output: null,\n            //     enableAutoScopeInvalidation: true,\n            // },\n        },\n    },\n\n    tag: {\n        list: {\n            get: {\n                input: Tag.GetTagsInputSchema,\n                output: Tag.GetTagsOutputSchema,\n                cacheControl: CACHE_CONTROL_HOUR,\n            },\n        },\n        post: {\n            input: Tag.CreateTagInputSchema,\n            output: Common.ObjectWithIdSchema,\n            autoScopeInvalidationDepth: 1,\n        },\n        patch: {\n            input: Tag.UpdateTagInputSchema,\n            output: null,\n            autoScopeInvalidationDepth: 1,\n        },\n        delete: {\n            input: Common.ObjectWithIdSchema,\n            output: null,\n            autoScopeInvalidationDepth: 1,\n        },\n    },\n\n    user: {\n        list: {\n            get: {\n                input: User.GetUsersInputSchema,\n                output: User.GetUsersOutputSchema,\n                cacheControl: DEFAULT_CACHE_CONTROL,\n            },\n        },\n        me: {\n            get: {\n                input: null,\n                output: User.GetMeOutputSchema,\n                cacheControl: CACHE_CONTROL_HOUR,\n            },\n        },\n        patch: {\n            input: User.UpdateUserInputSchema,\n            output: null,\n            autoScopeInvalidationDepth: 1,\n        },\n        title: {\n            list: {\n                get: {\n                    input: User.GetUserTitlesInputSchema,\n                    output: User.GetUserTitlesOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                },\n            },\n            post: {\n                input: User.CreateUserTitleInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            patch: {\n                input: User.UpdateUserTitleInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: Common.ObjectWithIdSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n        },\n        note: {\n            get: {\n                input: User.GetUserNoteInputSchema,\n                output: User.GetUserNoteOutputSchema,\n                cacheControl: DEFAULT_CACHE_CONTROL,\n            },\n            put: {\n                input: User.UpdateUserNoteInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n        },\n        invite: {\n            list: {\n                get: {\n                    input: User.GetUserInvitesInputSchema,\n                    output: User.GetUserInvitesOutputSchema,\n                    cacheControl: DEFAULT_CACHE_CONTROL,\n                },\n            },\n            put: {\n                input: User.UpsertUserInviteInputSchema,\n                output: Common.ObjectWithIdSchema,\n                autoScopeInvalidationDepth: 1,\n            },\n            delete: {\n                input: User.DeleteUserInviteInputSchema,\n                output: null,\n                autoScopeInvalidationDepth: 1,\n            },\n        },\n    },\n} satisfies Schema;\n\nexport const transformer = superjsonTransformer;\n"], "mappings": ";;;;;;;;;;;;;;;AAaO,IAAM,4BAA4B;AAClC,IAAM,qBAAqB;AAC3B,IAAM,0BAA0B;AAEhC,IAAM,wBAAwB;AAE9B,IAAM,SAAS;AAAA,EAClB,MAAM;AAAA,IACF,KAAK;AAAA,MACD,MAAM;AAAA,QACF,OAAO,aAAK;AAAA,QACZ,QAAQ,aAAK;AAAA,QACb,gBAAgB;AAAA,MACpB;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,QACF,OAAO,aAAK;AAAA,QACZ,QAAQ,aAAK;AAAA,QACb,gBAAgB;AAAA,QAChB,YAAY,CAAC,UAAU;AAAA,MAC3B;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,QACF,OAAO,aAAK;AAAA,QACZ,QAAQ,aAAK;AAAA,QACb,gBAAgB;AAAA,QAChB,YAAY,CAAC,UAAU;AAAA,MAC3B;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,KAAK;AAAA,QACD,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,YAAY,CAAC,UAAU;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACL,oBAAoB;AAAA,MAChB,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,KAAK;AAAA,QACD,OAAO,gBAAQ;AAAA,QACf,QAAQ,gBAAQ;AAAA,QAChB,cAAc;AAAA,QACd,oBAAoB;AAAA,MACxB;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,OAAO,gBAAQ;AAAA,MACf,QAAQ,eAAO;AAAA,MACf,4BAA4B;AAAA,IAChC;AAAA,IACA,OAAO;AAAA,MACH,OAAO,gBAAQ;AAAA,MACf,QAAQ;AAAA,MACR,4BAA4B;AAAA,IAChC;AAAA,IACA,QAAQ;AAAA,MACJ,OAAO,eAAO;AAAA,MACd,QAAQ;AAAA,MACR,4BAA4B;AAAA,IAChC;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,UACd,oBAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,eAAO;AAAA,QACd,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,eAAO;AAAA,QACd,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,UACF,OAAO,eAAO;AAAA,UACd,QAAQ;AAAA;AAAA,UAER,YAAY,CAAC,UAAU;AAAA,QAC3B;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,UACF,OAAO,eAAO;AAAA,UACd,QAAQ;AAAA;AAAA,UAER,YAAY,CAAC,UAAU;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,eAAO;AAAA,QACd,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,UACF,OAAO,eAAO;AAAA,UACd,QAAQ;AAAA;AAAA,UAER,YAAY,CAAC,UAAU;AAAA,QAC3B;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,UACF,OAAO,eAAO;AAAA,UACd,QAAQ;AAAA;AAAA,UAER,YAAY,CAAC,UAAU;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,eAAO;AAAA,UACd,QAAQ,eAAO;AAAA,UACf,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,eAAO;AAAA,QACd,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,QAC5B,YAAY,CAAC,iBAAiB;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,eAAO;AAAA,UACd,QAAQ,eAAO;AAAA,UACf,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,eAAO;AAAA,QACd,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,QAC5B,YAAY,CAAC,iBAAiB;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,KAAK;AAAA,QACD,OAAO,eAAO;AAAA,QACd,QAAQ,eAAO;AAAA,QACf,cAAc;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACL,MAAM;AAAA,MACF,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,UACd,oBAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,QACH,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,UACF,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,4BAA4B;AAAA,QAChC;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR,MAAM;AAAA,UACF,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,4BAA4B;AAAA,QAChC;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,UACF,KAAK;AAAA,YACD,OAAO,gBAAQ;AAAA,YACf,QAAQ,gBAAQ;AAAA,YAChB,cAAc;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,UACd,oBAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,QACH,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,UACF,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,4BAA4B;AAAA,QAChC;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,QACP,MAAM;AAAA,UACF,OAAO,gBAAQ;AAAA,UACf,QAAQ;AAAA,UACR,4BAA4B;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO;AAAA,UACP,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,QACH,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,eAAO;AAAA,QACd,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,MACD,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,UACd,oBAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,QACH,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMJ;AAAA,IACA,WAAW;AAAA,MACP,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,gBAAQ;AAAA,UACf,QAAQ,gBAAQ;AAAA,UAChB,cAAc;AAAA,UACd,oBAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,gBAAQ;AAAA,QACf,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,QACH,OAAO,gBAAQ;AAAA,QACf,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMJ;AAAA,EACJ;AAAA,EAEA,KAAK;AAAA,IACD,MAAM;AAAA,MACF,KAAK;AAAA,QACD,OAAO,YAAI;AAAA,QACX,QAAQ,YAAI;AAAA,QACZ,cAAc;AAAA,MAClB;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,OAAO,YAAI;AAAA,MACX,QAAQ,eAAO;AAAA,MACf,4BAA4B;AAAA,IAChC;AAAA,IACA,OAAO;AAAA,MACH,OAAO,YAAI;AAAA,MACX,QAAQ;AAAA,MACR,4BAA4B;AAAA,IAChC;AAAA,IACA,QAAQ;AAAA,MACJ,OAAO,eAAO;AAAA,MACd,QAAQ;AAAA,MACR,4BAA4B;AAAA,IAChC;AAAA,EACJ;AAAA,EAEA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,KAAK;AAAA,QACD,OAAO,aAAK;AAAA,QACZ,QAAQ,aAAK;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ;AAAA,IACA,IAAI;AAAA,MACA,KAAK;AAAA,QACD,OAAO;AAAA,QACP,QAAQ,aAAK;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,MACH,OAAO,aAAK;AAAA,MACZ,QAAQ;AAAA,MACR,4BAA4B;AAAA,IAChC;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,aAAK;AAAA,UACZ,QAAQ,aAAK;AAAA,UACb,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO,aAAK;AAAA,QACZ,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,QACH,OAAO,aAAK;AAAA,QACZ,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,eAAO;AAAA,QACd,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,KAAK;AAAA,QACD,OAAO,aAAK;AAAA,QACZ,QAAQ,aAAK;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,QACD,OAAO,aAAK;AAAA,QACZ,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,QACF,KAAK;AAAA,UACD,OAAO,aAAK;AAAA,UACZ,QAAQ,aAAK;AAAA,UACb,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,KAAK;AAAA,QACD,OAAO,aAAK;AAAA,QACZ,QAAQ,eAAO;AAAA,QACf,4BAA4B;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,QACJ,OAAO,aAAK;AAAA,QACZ,QAAQ;AAAA,QACR,4BAA4B;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACJ;AAEO,IAAM,cAAc;", "names": []}